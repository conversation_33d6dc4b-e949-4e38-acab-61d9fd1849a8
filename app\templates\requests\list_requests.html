{% extends "base.html" %}

{% block title %}طلباتي - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-file-earmark-text"></i>
        طلباتي
    </h2>
    <a href="/requests/new" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i>
        طلب جديد
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if requests %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>اسم الطلب</th>
                        <th>العنوان</th>
                        <th>الحالة</th>
                        <th>المرفقات</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in requests %}
                    <tr>
                        <td>
                            <code>{{ req.request_number }}</code>
                        </td>
                        <td>{{ req.request_name }}</td>
                        <td>{{ req.request_title }}</td>
                        <td>
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning status-badge">قيد المراجعة</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info status-badge">قيد التنفيذ</span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success status-badge">مكتمل</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger status-badge">مرفوض</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ req.files|length }}</span>
                        </td>
                        <td>{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye"></i>
                                عرض
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-file-earmark-text display-4 text-muted"></i>
            <h4 class="mt-3 text-muted">لا توجد طلبات</h4>
            <p class="text-muted">لم تقم بإنشاء أي طلبات حتى الآن</p>
            <a href="/requests/new" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                إنشاء طلب جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
