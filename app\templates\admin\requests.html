{% extends "base.html" %}

{% block title %}إدارة الطلبات - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-file-earmark-text"></i>
        إدارة الطلبات
    </h2>
    <div>
        <div class="btn-group" role="group">
            <a href="/admin/requests" class="btn btn-outline-primary {% if not current_status %}active{% endif %}">
                الكل
            </a>
            {% for status in statuses %}
            <a href="/admin/requests?status={{ status }}" 
               class="btn btn-outline-primary {% if current_status == status %}active{% endif %}">
                {% if status == 'pending' %}قيد المراجعة
                {% elif status == 'in_progress' %}قيد التنفيذ
                {% elif status == 'completed' %}مكتملة
                {% elif status == 'rejected' %}مرفوضة
                {% endif %}
            </a>
            {% endfor %}
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if requests %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العنوان</th>
                        <th>مقدم الطلب</th>
                        <th>الحالة</th>
                        <th>المرفقات</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in requests %}
                    <tr>
                        <td>
                            <code>{{ req.request_number }}</code>
                        </td>
                        <td>{{ req.request_title }}</td>
                        <td>{{ req.user.full_name }}</td>
                        <td>
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning status-badge">قيد المراجعة</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info status-badge">قيد التنفيذ</span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success status-badge">مكتمل</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger status-badge">مرفوض</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ req.files|length }}</span>
                        </td>
                        <td>{{ req.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary me-1">
                                <i class="bi bi-eye"></i>
                            </a>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-gear"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="pending">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-clock text-warning"></i> قيد المراجعة
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="in_progress">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle text-info"></i> قيد التنفيذ
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-check-circle text-success"></i> مكتمل
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-x-circle text-danger"></i> مرفوض
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-file-earmark-text display-4 text-muted"></i>
            <h4 class="mt-3 text-muted">لا توجد طلبات</h4>
            {% if current_status %}
            <p class="text-muted">لا توجد طلبات بحالة "{{ current_status }}"</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
