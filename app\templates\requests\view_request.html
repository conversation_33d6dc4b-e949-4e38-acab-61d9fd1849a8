{% extends "base.html" %}

{% block title %}عرض الطلب {{ req.request_number }} - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-file-earmark-text"></i>
        تفاصيل الطلب
    </h2>
    <div>
        <a href="/requests" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للطلبات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ req.request_title }}</h5>
                    {% if req.status.value == 'pending' %}
                    <span class="badge bg-warning fs-6">قيد المراجعة</span>
                    {% elif req.status.value == 'in_progress' %}
                    <span class="badge bg-info fs-6">قيد التنفيذ</span>
                    {% elif req.status.value == 'completed' %}
                    <span class="badge bg-success fs-6">مكتمل</span>
                    {% elif req.status.value == 'rejected' %}
                    <span class="badge bg-danger fs-6">مرفوض</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الطلب:</strong>
                        <code class="ms-2">{{ req.request_number }}</code>
                    </div>
                    <div class="col-md-6">
                        <strong>الرمز التعريفي:</strong>
                        <code class="ms-2">{{ req.unique_code }}</code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>اسم الطلب:</strong>
                        <span class="ms-2">{{ req.request_name }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>مقدم الطلب:</strong>
                        <span class="ms-2">{{ req.user.full_name }}</span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>تاريخ الإنشاء:</strong>
                        <span class="ms-2">{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>آخر تحديث:</strong>
                        <span class="ms-2">
                            {% if req.updated_at %}
                            {{ req.updated_at.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                            لم يتم التحديث
                            {% endif %}
                        </span>
                    </div>
                </div>

                {% if req.description %}
                <div class="mb-3">
                    <strong>الوصف:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ req.description }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Status Timeline -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-clock-history"></i>
                    مراحل الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item {% if req.status.value in ['pending', 'in_progress', 'completed', 'rejected'] %}active{% endif %}">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تم إنشاء الطلب</h6>
                            <p class="timeline-text">{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                    
                    {% if req.status.value in ['in_progress', 'completed'] %}
                    <div class="timeline-item active">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">قيد التنفيذ</h6>
                            <p class="timeline-text">تم قبول الطلب وبدء التنفيذ</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if req.status.value == 'completed' %}
                    <div class="timeline-item active">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">مكتمل</h6>
                            <p class="timeline-text">تم إنجاز الطلب بنجاح</p>
                        </div>
                    </div>
                    {% elif req.status.value == 'rejected' %}
                    <div class="timeline-item active">
                        <div class="timeline-marker bg-danger"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">مرفوض</h6>
                            <p class="timeline-text">تم رفض الطلب</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Files -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-paperclip"></i>
                    المرفقات ({{ req.files|length }})
                </h6>
            </div>
            <div class="card-body">
                {% if req.files %}
                <div class="list-group list-group-flush">
                    {% for file in req.files %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <i class="bi bi-file-earmark me-2"></i>
                            <span class="small">{{ file.original_filename }}</span>
                        </div>
                        <div>
                            <span class="badge bg-secondary">{{ file.file_size_mb }} MB</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="bi bi-file-earmark-x display-6"></i>
                    <p class="mt-2">لا توجد مرفقات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-item:not(.active) .timeline-marker {
    background: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}
</style>
{% endblock %}
