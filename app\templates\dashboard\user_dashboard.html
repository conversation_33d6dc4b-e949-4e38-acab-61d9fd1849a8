{% extends "base.html" %}

{% block title %}لوحة التحكم - CMSVS{% endblock %}

{% block content %}
<div class="user-dashboard">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-house"></i>
        مرحباً، {{ current_user.full_name }}
    </h2>
    <a href="/requests/new" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i>
        طلب جديد
    </a>
</div>

<!-- Personal Progress Tracking Widget -->
{% if user_progress %}
<div class="row mb-4">
    <div class="col-12">
        <div class="user-progress-widget">
            <div class="user-progress-header">
                <h5 class="user-progress-title">
                    <i class="bi bi-speedometer2"></i>
                    تقدمك الشخصي
                </h5>
                <p class="user-progress-subtitle">تتبع أهدافك اليومية والأسبوعية والشهرية</p>
            </div>

            <div class="user-progress-body">
                <div class="row">
                    <!-- Daily Progress -->
                    <div class="col-md-4">
                        <div class="progress-metric">
                            <div class="progress-metric-header">
                                <div class="d-flex align-items-center">
                                    <div class="progress-icon daily">
                                        <i class="bi bi-sun"></i>
                                    </div>
                                    <span class="progress-metric-label">الهدف اليومي</span>
                                </div>
                                <span class="progress-metric-value {{ user_progress.daily.status }}">
                                    {{ user_progress.daily.completed }}/{{ user_progress.daily.goal }} - {{ user_progress.daily.percentage }}%
                                </span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill {{ user_progress.daily.status }}"
                                     style="width: {{ user_progress.daily.percentage }}%"></div>
                            </div>
                            <div class="progress-remaining-requests {% if user_progress.daily.remaining == 0 %}achieved{% endif %}">
                                {% if user_progress.daily.remaining == 0 %}
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% else %}
                                <i class="bi bi-target me-1"></i>
                                {% endif %}
                                {{ user_progress.daily.remaining_text }}
                            </div>
                        </div>
                    </div>

                    <!-- Weekly Progress -->
                    <div class="col-md-4">
                        <div class="progress-metric">
                            <div class="progress-metric-header">
                                <div class="d-flex align-items-center">
                                    <div class="progress-icon weekly">
                                        <i class="bi bi-calendar-week"></i>
                                    </div>
                                    <span class="progress-metric-label">الهدف الأسبوعي</span>
                                </div>
                                <span class="progress-metric-value {{ user_progress.weekly.status }}">
                                    {{ user_progress.weekly.completed }}/{{ user_progress.weekly.goal }} - {{ user_progress.weekly.percentage }}%
                                </span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill {{ user_progress.weekly.status }}"
                                     style="width: {{ user_progress.weekly.percentage }}%"></div>
                            </div>
                            <div class="progress-remaining-requests {% if user_progress.weekly.remaining == 0 %}achieved{% endif %}">
                                {% if user_progress.weekly.remaining == 0 %}
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% else %}
                                <i class="bi bi-bullseye me-1"></i>
                                {% endif %}
                                {{ user_progress.weekly.remaining_text }}
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Progress -->
                    <div class="col-md-4">
                        <div class="progress-metric">
                            <div class="progress-metric-header">
                                <div class="d-flex align-items-center">
                                    <div class="progress-icon monthly">
                                        <i class="bi bi-calendar-month"></i>
                                    </div>
                                    <span class="progress-metric-label">الهدف الشهري</span>
                                </div>
                                <span class="progress-metric-value {{ user_progress.monthly.status }}">
                                    {{ user_progress.monthly.completed }}/{{ user_progress.monthly.goal }} - {{ user_progress.monthly.percentage }}%
                                </span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill {{ user_progress.monthly.status }}"
                                     style="width: {{ user_progress.monthly.percentage }}%"></div>
                            </div>
                            <div class="progress-remaining-requests {% if user_progress.monthly.remaining == 0 %}achieved{% endif %}">
                                {% if user_progress.monthly.remaining == 0 %}
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% else %}
                                <i class="bi bi-trophy me-1"></i>
                                {% endif %}
                                {{ user_progress.monthly.remaining_text }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Summary -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="progress-summary">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex gap-3">
                                    <div class="summary-item">
                                        <span class="summary-label">الأهداف المحققة:</span>
                                        <span class="summary-value text-success">
                                            {% set achieved_goals = 0 %}
                                            {% if user_progress.daily.remaining == 0 %}{% set achieved_goals = achieved_goals + 1 %}{% endif %}
                                            {% if user_progress.weekly.remaining == 0 %}{% set achieved_goals = achieved_goals + 1 %}{% endif %}
                                            {% if user_progress.monthly.remaining == 0 %}{% set achieved_goals = achieved_goals + 1 %}{% endif %}
                                            {{ achieved_goals }}/3
                                        </span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">إجمالي الطلبات المكتملة اليوم:</span>
                                        <span class="summary-value text-primary">{{ user_progress.daily.completed }}</span>
                                    </div>
                                </div>
                                <div class="progress-actions">
                                    <a href="/requests/new" class="btn btn-primary btn-sm">
                                        <i class="bi bi-plus-circle me-1"></i>
                                        طلب جديد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card card-stats bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ stats.total }}</h3>
                        <p class="card-text">إجمالي الطلبات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-stats bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ stats.pending }}</h3>
                        <p class="card-text">طلبات قيد المراجعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-stats bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ stats.completed }}</h3>
                        <p class="card-text">طلبات مكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Requests -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-text"></i>
                    الطلبات الأخيرة
                </h5>
                <a href="/requests" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if requests %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العنوان</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for req in requests %}
                            <tr>
                                <td>
                                    <code>{{ req.request_number }}</code>
                                </td>
                                <td>{{ req.request_title }}</td>
                                <td>
                                    {% if req.status.value == 'pending' %}
                                    <span class="badge bg-warning status-badge">قيد المراجعة</span>
                                    {% elif req.status.value == 'in_progress' %}
                                    <span class="badge bg-info status-badge">قيد التنفيذ</span>
                                    {% elif req.status.value == 'completed' %}
                                    <span class="badge bg-success status-badge">مكتمل</span>
                                    {% elif req.status.value == 'rejected' %}
                                    <span class="badge bg-danger status-badge">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>{{ req.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-file-earmark-text display-4 text-muted"></i>
                    <p class="text-muted mt-2">لا توجد طلبات حتى الآن</p>
                    <a href="/requests/new" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء طلب جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i>
                    النشاطات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if activities %}
                {% for activity in activities %}
                <div class="activity-item">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            {% if activity.activity_type.value == 'login' %}
                            <i class="bi bi-box-arrow-in-right text-success"></i>
                            {% elif activity.activity_type.value == 'request_created' %}
                            <i class="bi bi-plus-circle text-primary"></i>
                            {% elif activity.activity_type.value == 'file_uploaded' %}
                            <i class="bi bi-upload text-info"></i>
                            {% else %}
                            <i class="bi bi-activity text-secondary"></i>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 small">{{ activity.description }}</p>
                            <small class="text-muted">{{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-activity display-6 text-muted"></i>
                    <p class="text-muted mt-2 small">لا توجد نشاطات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize user progress widget
document.addEventListener('DOMContentLoaded', function() {
    initializeUserProgressWidget();
});

function initializeUserProgressWidget() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-bar-fill');
    progressBars.forEach((bar, index) => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';

        setTimeout(() => {
            bar.style.width = targetWidth;
        }, 300 + (index * 200));
    });

    // Add hover effects to progress metrics
    const metrics = document.querySelectorAll('.progress-metric');
    metrics.forEach(metric => {
        metric.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 0.25rem 0.5rem rgba(0, 0, 0, 0.1)';
        });

        metric.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Animate achievement indicators
    const achievedElements = document.querySelectorAll('.progress-remaining-requests.achieved');
    achievedElements.forEach((element, index) => {
        setTimeout(() => {
            element.style.animation = 'pulse 1s ease-in-out';
        }, 1000 + (index * 300));
    });

    // Add motivational messages based on progress
    addMotivationalMessages();
}

function addMotivationalMessages() {
    const progressWidget = document.querySelector('.user-progress-widget');
    if (!progressWidget) return;

    const dailyStatus = document.querySelector('.progress-metric-value.success');
    const weeklyStatus = document.querySelector('.progress-metric-value.warning');
    const monthlyStatus = document.querySelector('.progress-metric-value.danger');

    // You can add motivational tooltips or messages here
    // For example, show encouraging messages for users who are behind
}

// Add pulse animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .progress-remaining-requests.achieved {
        animation: pulse 2s ease-in-out infinite;
    }

    .progress-bar-fill {
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .user-dashboard .progress-metric {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
