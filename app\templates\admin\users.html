{% extends "base.html" %}

{% block title %}إدارة المستخدمين - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-people"></i>
        إدارة المستخدمين
    </h2>
    <a href="/admin/users/new" class="btn btn-primary">
        <i class="bi bi-person-plus"></i>
        إنشاء مستخدم جديد
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if users %}
        <!-- Bulk Actions -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-body py-3">
                <form method="post" action="/admin/users/bulk-action" id="bulkUserForm">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <select class="form-select" name="action" required>
                                <option value="">اختر العملية</option>
                                <option value="activate">تفعيل المحدد</option>
                                <option value="deactivate">إلغاء تفعيل المحدد</option>
                                <option value="make_admin">ترقية إلى مدير</option>
                                <option value="make_user">تحويل إلى مستخدم عادي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary" id="bulkActionBtn" disabled>
                                <i class="bi bi-gear"></i>
                                تطبيق على المحدد
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <span id="selectedCount">0</span> مستخدم محدد
                            </small>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>المعرف</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>
                            {% if user.id != current_user.id %}
                            <input type="checkbox" name="user_ids" value="{{ user.id }}"
                                   class="form-check-input user-checkbox" form="bulkUserForm">
                            {% endif %}
                        </td>
                        <td>{{ user.id }}</td>
                        <td>
                            <strong>{{ user.username }}</strong>
                        </td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.role.value == 'admin' %}
                            <span class="badge bg-danger">مدير</span>
                            {% else %}
                            <span class="badge bg-primary">مستخدم</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if user.id != current_user.id %}
                            <div class="d-flex gap-2">
                                <!-- Status Toggle -->
                                <form method="post" action="/admin/users/{{ user.id }}/toggle-status" class="d-inline">
                                    {% if user.is_active %}
                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                            onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                        <i class="bi bi-person-x"></i>
                                        إلغاء التفعيل
                                    </button>
                                    {% else %}
                                    <button type="submit" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-person-check"></i>
                                        تفعيل
                                    </button>
                                    {% endif %}
                                </form>

                                <!-- Edit User -->
                                <a href="/admin/users/{{ user.id }}/edit" class="btn btn-sm btn-outline-info">
                                    <i class="bi bi-pencil-square"></i>
                                    تعديل
                                </a>

                                <!-- Role Management -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-shield-check"></i>
                                        تغيير الدور
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% if user.role.value != 'user' %}
                                        <li>
                                            <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline">
                                                <input type="hidden" name="role" value="user">
                                                <button type="submit" class="dropdown-item"
                                                        onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مستخدم عادي؟')">
                                                    <i class="bi bi-person text-info"></i> مستخدم عادي
                                                </button>
                                            </form>
                                        </li>
                                        {% endif %}
                                        {% if user.role.value != 'admin' %}
                                        <li>
                                            <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline">
                                                <input type="hidden" name="role" value="admin">
                                                <button type="submit" class="dropdown-item"
                                                        onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مدير نظام؟')">
                                                    <i class="bi bi-shield-check text-warning"></i> مدير النظام
                                                </button>
                                            </form>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                            {% else %}
                            <span class="text-muted small">حسابك الحالي</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people display-4 text-muted"></i>
            <h4 class="mt-3 text-muted">لا يوجد مستخدمون</h4>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const bulkForm = document.getElementById('bulkUserForm');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
    }

    // Individual checkbox change
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // Update select all checkbox state
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
            }
        });
    });

    // Update selected count and button state
    function updateSelectedCount() {
        const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
        if (selectedCountSpan) selectedCountSpan.textContent = checkedCount;
        if (bulkActionBtn) bulkActionBtn.disabled = checkedCount === 0;
    }

    // Form submission confirmation
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            const action = this.querySelector('select[name="action"]').value;

            if (checkedCount === 0) {
                e.preventDefault();
                alert('يرجى اختيار مستخدم واحد على الأقل');
                return;
            }

            const actionNames = {
                'activate': 'تفعيل',
                'deactivate': 'إلغاء تفعيل',
                'make_admin': 'ترقية إلى مدير',
                'make_user': 'تحويل إلى مستخدم عادي'
            };

            const actionName = actionNames[action] || action;

            if (!confirm(`هل أنت متأكد من ${actionName} ${checkedCount} مستخدم؟`)) {
                e.preventDefault();
                return;
            }

            // Show loading state
            bulkActionBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري التطبيق...';
            bulkActionBtn.disabled = true;
        });
    }

    // Initialize
    updateSelectedCount();
});
</script>
{% endblock %}
