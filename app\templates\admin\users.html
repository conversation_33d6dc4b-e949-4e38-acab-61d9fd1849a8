{% extends "base.html" %}

{% block title %}إدارة المستخدمين - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-people"></i>
        إدارة المستخدمين
    </h2>
</div>

<div class="card">
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>
                            <strong>{{ user.username }}</strong>
                        </td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.role.value == 'admin' %}
                            <span class="badge bg-danger">مدير</span>
                            {% else %}
                            <span class="badge bg-primary">مستخدم</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if user.id != current_user.id %}
                            <form method="post" action="/admin/users/{{ user.id }}/toggle-status" class="d-inline">
                                {% if user.is_active %}
                                <button type="submit" class="btn btn-sm btn-outline-warning" 
                                        onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                    <i class="bi bi-person-x"></i>
                                    إلغاء التفعيل
                                </button>
                                {% else %}
                                <button type="submit" class="btn btn-sm btn-outline-success">
                                    <i class="bi bi-person-check"></i>
                                    تفعيل
                                </button>
                                {% endif %}
                            </form>
                            {% else %}
                            <span class="text-muted small">حسابك الحالي</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people display-4 text-muted"></i>
            <h4 class="mt-3 text-muted">لا يوجد مستخدمون</h4>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
