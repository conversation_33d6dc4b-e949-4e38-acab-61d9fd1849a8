<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CMSVS Internal System{% endblock %}</title>
    
    <!-- Remove problematic preload -->

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Bootstrap Icons - Primary CDN with fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet" crossorigin="anonymous" onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.3/font/bootstrap-icons.min.css';">

    <!-- Font Awesome as fallback -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    
    <!-- Custom CSS - Soft UI Design -->
    <style>
        :root {
            --soft-bg: #f8f9fa;
            --soft-white: #ffffff;
            --soft-primary: #5e72e4;
            --soft-secondary: #8392ab;
            --soft-success: #2dce89;
            --soft-info: #11cdef;
            --soft-warning: #fb6340;
            --soft-danger: #f5365c;
            --soft-dark: #32325d;
            --soft-light: #e9ecef;
            --soft-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12), 0 0.125rem 0.25rem -0.0625rem rgba(20, 20, 20, 0.07);
            --soft-shadow-lg: 0 0.5rem 1rem -0.25rem rgba(20, 20, 20, 0.15), 0 0.25rem 0.5rem -0.125rem rgba(20, 20, 20, 0.1);
            --soft-shadow-inset: inset 0 1px 2px rgba(20, 20, 20, 0.1);
            --soft-border-radius: 0.75rem;
            --soft-border-radius-lg: 1rem;
            --soft-border-radius-xl: 1.5rem;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--soft-dark);
        }

        /* Navbar Soft UI */
        .navbar {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%) !important;
            box-shadow: var(--soft-shadow);
            border-radius: 0 0 var(--soft-border-radius) var(--soft-border-radius);
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            text-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--soft-border-radius);
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        /* Sidebar Soft UI */
        .sidebar {
            min-height: calc(100vh - 56px);
            background: var(--soft-white);
            box-shadow: var(--soft-shadow);
            border-radius: var(--soft-border-radius);
            margin: 1rem 0.5rem;
            padding: 1.5rem !important;
        }

        .list-group-item {
            border: none;
            background: transparent;
            border-radius: var(--soft-border-radius) !important;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            color: var(--soft-secondary);
        }

        .list-group-item:hover, .list-group-item.active {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
            transform: translateX(5px);
            box-shadow: var(--soft-shadow);
        }

        .list-group-item i {
            margin-left: 0.75rem;
            width: 1.25rem;
            text-align: center;
            font-family: "bootstrap-icons" !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Navigation Icons Enhancement */
        .navbar .bi,
        .nav-link .bi {
            margin-inline-end: 0.5rem !important;
            font-size: 1rem !important;
            vertical-align: -0.125em !important;
            color: white !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
        }

        /* Navbar brand icon */
        .navbar-brand .bi {
            color: white !important;
            font-size: 1.5rem !important;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
        }

        /* Sidebar Icons Enhancement */
        .sidebar .bi,
        .list-group-item .bi {
            margin-inline-end: 0.75rem !important;
            width: 1.25rem !important;
            text-align: center !important;
            flex-shrink: 0 !important;
            color: var(--soft-secondary) !important;
            transition: color 0.3s ease !important;
        }

        /* Sidebar hover and active states */
        .list-group-item:hover .bi,
        .list-group-item.active .bi {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
        }

        /* Main Content */
        .main-content {
            min-height: calc(100vh - 56px);
            padding: 2rem !important;
        }

        /* Cards Soft UI */
        .card {
            border: none;
            border-radius: var(--soft-border-radius-lg);
            box-shadow: var(--soft-shadow);
            background: var(--soft-white);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--soft-shadow-lg);
        }

        .card-header {
            background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            border-radius: var(--soft-border-radius-lg) var(--soft-border-radius-lg) 0 0 !important;
            padding: 1.5rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Statistics Cards */
        .card-stats {
            border: none;
            position: relative;
            overflow: hidden;
        }

        .card-stats.bg-primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%) !important;
        }

        .card-stats.bg-success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%) !important;
        }

        .card-stats.bg-info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%) !important;
        }

        .card-stats.bg-warning {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%) !important;
        }

        .card-stats.bg-danger {
            background: linear-gradient(135deg, var(--soft-danger) 0%, #ff1744 100%) !important;
        }

        .card-stats::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Buttons Soft UI */
        .btn {
            border-radius: var(--soft-border-radius);
            font-weight: 600;
            text-transform: none;
            transition: all 0.3s ease;
            border: none;
            box-shadow: var(--soft-shadow);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--soft-shadow-lg);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--soft-danger) 0%, #ff1744 100%);
        }

        .btn-outline-primary {
            border: 2px solid var(--soft-primary);
            color: var(--soft-primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--soft-primary);
            color: white;
        }

        /* Tables Soft UI */
        .table {
            border-radius: var(--soft-border-radius);
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--soft-light) 0%, #f8f9fa 100%);
            border: none;
            font-weight: 600;
            color: var(--soft-dark);
            padding: 1rem;
        }

        .table tbody td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: rgba(94, 114, 228, 0.05);
            transform: scale(1.01);
        }

        /* Badges Soft UI */
        .badge {
            border-radius: var(--soft-border-radius);
            font-weight: 600;
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
        }

        .bg-warning {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%) !important;
        }

        .bg-success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%) !important;
        }

        .bg-danger {
            background: linear-gradient(135deg, var(--soft-danger) 0%, #ff1744 100%) !important;
        }

        /* Forms Soft UI */
        .form-control, .form-select {
            border: 2px solid rgba(0,0,0,0.1);
            border-radius: var(--soft-border-radius);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: var(--soft-white);
            box-shadow: var(--soft-shadow-inset);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--soft-primary);
            box-shadow: 0 0 0 0.2rem rgba(94, 114, 228, 0.25);
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 600;
            color: var(--soft-dark);
            margin-bottom: 0.75rem;
        }

        /* File Upload Area */
        .file-upload-area {
            border: 2px dashed rgba(94, 114, 228, 0.3);
            border-radius: var(--soft-border-radius-lg);
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.05) 0%, rgba(255,255,255,0.8) 100%);
        }

        .file-upload-area:hover {
            border-color: var(--soft-primary);
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.1) 0%, rgba(255,255,255,0.9) 100%);
            transform: translateY(-2px);
            box-shadow: var(--soft-shadow);
        }

        /* Alerts Soft UI */
        .alert {
            border: none;
            border-radius: var(--soft-border-radius);
            box-shadow: var(--soft-shadow);
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(45, 206, 137, 0.1) 0%, rgba(86, 202, 0, 0.05) 100%);
            color: var(--soft-success);
            border-left: 4px solid var(--soft-success);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(245, 54, 92, 0.1) 0%, rgba(255, 23, 68, 0.05) 100%);
            color: var(--soft-danger);
            border-left: 4px solid var(--soft-danger);
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(251, 99, 64, 0.1) 0%, rgba(255, 138, 0, 0.05) 100%);
            color: var(--soft-warning);
            border-left: 4px solid var(--soft-warning);
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(17, 205, 239, 0.1) 0%, rgba(17, 113, 239, 0.05) 100%);
            color: var(--soft-info);
            border-left: 4px solid var(--soft-info);
        }

        /* Activity Items */
        .activity-item {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(94, 114, 228, 0.05);
            border-radius: var(--soft-border-radius);
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* Status Badge */
        .status-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: var(--soft-border-radius);
        }

        /* Dropdown Soft UI */
        .dropdown-menu {
            border: none;
            border-radius: var(--soft-border-radius);
            box-shadow: var(--soft-shadow-lg);
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: var(--soft-border-radius);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
            transform: translateX(5px);
        }

        /* Animations */
        @keyframes softFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card, .alert {
            animation: softFadeIn 0.6s ease-out;
        }

        /* Avatar Sizes */
        .avatar-sm {
            width: 2.5rem;
            height: 2.5rem;
        }

        .avatar-lg {
            width: 4rem;
            height: 4rem;
        }

        /* Enhanced Card Hover Effects */
        .card-stats:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 1rem 2rem -0.5rem rgba(20, 20, 20, 0.2), 0 0.5rem 1rem -0.25rem rgba(20, 20, 20, 0.15);
        }

        /* Text Utilities */
        .text-white-50 {
            color: rgba(255, 255, 255, 0.5) !important;
        }

        .text-white-75 {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Display Utilities */
        .display-5 {
            font-size: 2.5rem;
            font-weight: 300;
            line-height: 1.2;
        }

        /* Enhanced Gradients for Better Visual Appeal */
        .bg-gradient {
            background-image: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        }

        /* Improved Spacing */
        .g-4 > * {
            padding: 1rem;
        }

        /* Enhanced Typography */
        .fw-semibold {
            font-weight: 600;
        }

        .display-6 {
            font-size: 2rem;
            font-weight: 600;
            line-height: 1.2;
        }



        /* Icon size utilities */
        .fs-3 {
            font-size: 1.75rem !important;
        }

        /* Specific icon fixes for statistics cards */
        .avatar-lg .bi {
            font-size: 1.75rem !important;
            line-height: 1 !important;
            display: inline-block !important;
            width: 1.75rem !important;
            height: 1.75rem !important;
            text-align: center !important;
            vertical-align: middle !important;
        }

        /* Remove margin from icons in avatar containers */
        .avatar-lg .bi, .avatar-sm .bi {
            margin-right: 0 !important;
            margin-left: 0 !important;
        }

        /* Ensure white text color for icons in colored backgrounds */
        .text-white {
            color: #ffffff !important;
        }

        /* Force icon visibility in all contexts */
        .card-stats .bi,
        .avatar-lg .bi,
        .avatar-sm .bi,
        .nav-link .bi,
        .list-group-item .bi {
            opacity: 1 !important;
            visibility: visible !important;
            display: inline-block !important;
        }

        /* Enhanced Icon Visibility and RTL Support */
        .bi {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            vertical-align: -.125em !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* ICON COLOR FIXES - Ensure proper contrast in all contexts */

        /* Default icon colors for light backgrounds */
        .bi {
            color: var(--soft-dark) !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: inline-block !important;
            position: relative !important;
            z-index: 10 !important;
        }

        /* Navigation bar icons - white on dark background */
        .navbar .bi,
        .navbar-brand .bi,
        .nav-link .bi {
            color: white !important;
        }

        /* Sidebar icons - dark on light background */
        .sidebar .bi,
        .list-group-item .bi {
            color: var(--soft-secondary) !important;
        }

        /* Sidebar active/hover state icons */
        .list-group-item:hover .bi,
        .list-group-item.active .bi {
            color: white !important;
        }

        /* Button icons inherit button text color */
        .btn .bi {
            color: inherit !important;
        }

        /* Specific button color overrides */
        .btn-primary .bi,
        .btn-success .bi,
        .btn-info .bi,
        .btn-warning .bi,
        .btn-danger .bi {
            color: white !important;
        }

        .btn-outline-primary .bi {
            color: var(--soft-primary) !important;
        }

        .btn-outline-primary:hover .bi {
            color: white !important;
        }

        /* Card header icons */
        .card-header .bi {
            color: var(--soft-dark) !important;
        }

        /* Statistics card icons on colored backgrounds */
        .card-stats .bi {
            color: white !important;
        }

        /* Avatar container icons */
        .avatar-lg .bi,
        .avatar-sm .bi {
            color: white !important;
        }

        /* Dropdown menu icons */
        .dropdown-item .bi {
            color: var(--soft-secondary) !important;
        }

        .dropdown-item:hover .bi {
            color: white !important;
        }

        /* Text color utility overrides for icons */
        .text-primary .bi {
            color: var(--soft-primary) !important;
        }

        .text-secondary .bi {
            color: var(--soft-secondary) !important;
        }

        .text-success .bi {
            color: var(--soft-success) !important;
        }

        .text-info .bi {
            color: var(--soft-info) !important;
        }

        .text-warning .bi {
            color: var(--soft-warning) !important;
        }

        .text-danger .bi {
            color: var(--soft-danger) !important;
        }

        .text-dark .bi {
            color: var(--soft-dark) !important;
        }

        .text-muted .bi {
            color: #6c757d !important;
        }

        .text-white .bi {
            color: white !important;
        }

        /* RTL Layout Support for Icons */
        [dir="rtl"] .bi {
            direction: ltr !important;
            unicode-bidi: bidi-override !important;
            transform: scaleX(-1) !important;
        }

        /* Prevent RTL flip for specific icons that should not be flipped */
        [dir="rtl"] .bi-arrow-left,
        [dir="rtl"] .bi-arrow-right,
        [dir="rtl"] .bi-chevron-left,
        [dir="rtl"] .bi-chevron-right {
            transform: none !important;
        }

        /* Dashboard Statistics Card Icons - Enhanced */
        .card-stats .avatar-lg .bi {
            color: white !important;
            font-size: 1.75rem !important;
            line-height: 1 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            height: 100% !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
        }

        /* Ensure avatar containers have proper background contrast */
        .avatar-lg.bg-white {
            background-color: rgba(255,255,255,0.2) !important;
        }

        .avatar-lg.bg-white .bi {
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.4) !important;
        }

        /* Fix for light background avatars */
        .card:not(.card-stats) .avatar-lg .bi {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
        }

        /* Specific fixes for different background colors */
        .bg-primary .bi,
        .bg-success .bi,
        .bg-info .bi,
        .bg-warning .bi,
        .bg-danger .bi,
        .bg-secondary .bi,
        .bg-dark .bi {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
        }

        .bg-light .bi,
        .bg-white .bi {
            color: var(--soft-dark) !important;
            text-shadow: none !important;
        }

        /* Override for gradient backgrounds */
        .bg-gradient .bi {
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
        }

        /* Small Avatar Icons */
        .avatar-sm .bi {
            font-size: 0.875rem !important;
            color: white !important;
        }

        /* Button Icons */
        .btn .bi {
            margin-inline-end: 0.375rem !important;
            font-size: 0.875rem !important;
        }

        /* Dropdown Item Icons */
        .dropdown-item .bi {
            margin-inline-end: 0.5rem !important;
            width: 1rem !important;
            text-align: center !important;
        }

        /* Gradient Background Icon Enhancement */
        .bg-gradient .bi {
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1)) !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
        }

        /* Icon Loading States */
        .icons-loading .bi {
            opacity: 0.5 !important;
        }

        .icons-loaded .bi {
            opacity: 1 !important;
            transition: opacity 0.3s ease !important;
        }

        .icons-fallback .bi {
            font-size: 1.2em !important;
            line-height: 1 !important;
        }

        /* Ensure icons don't break layout */
        .bi {
            min-width: 1em !important;
            text-align: center !important;
            speak: none !important;
        }

        /* Fix for icons in flex containers */
        .d-flex .bi,
        .flex-shrink-0 .bi {
            flex-shrink: 0 !important;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .bi {
                font-weight: bold !important;
            }

            .bg-gradient .bi {
                text-shadow: 1px 1px 0 rgba(0,0,0,0.8) !important;
            }
        }

        /* Print styles for icons */
        @media print {
            .bi::before {
                content: "[icon]" !important;
                font-family: system-ui, sans-serif !important;
            }
        }

        /* Additional icon visibility fixes */

        /* Table icons */
        .table .bi {
            color: var(--soft-secondary) !important;
        }

        .table .btn .bi {
            color: inherit !important;
        }

        /* Alert icons */
        .alert .bi {
            color: inherit !important;
        }

        /* Badge icons */
        .badge .bi {
            color: inherit !important;
        }

        /* Form icons */
        .form-control .bi,
        .input-group .bi {
            color: var(--soft-secondary) !important;
        }

        /* Breadcrumb icons */
        .breadcrumb .bi {
            color: var(--soft-secondary) !important;
        }

        /* Pagination icons */
        .pagination .bi {
            color: var(--soft-primary) !important;
        }

        /* Modal icons */
        .modal .bi {
            color: var(--soft-dark) !important;
        }

        .modal-header .bi {
            color: var(--soft-dark) !important;
        }

        /* Toast icons */
        .toast .bi {
            color: var(--soft-dark) !important;
        }

        /* Offcanvas icons */
        .offcanvas .bi {
            color: var(--soft-dark) !important;
        }

        /* Force visibility for all icon contexts */
        .bi {
            speak: none !important;
            font-variant: normal !important;
            text-transform: none !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Ensure icons don't inherit problematic styles */
        .bi:before {
            display: inline-block !important;
            text-decoration: none !important;
        }

        /* High contrast accessibility */
        @media (prefers-contrast: high) {
            .bi {
                font-weight: bold !important;
                text-shadow: none !important;
                filter: none !important;
            }

            .navbar .bi,
            .card-stats .bi,
            .avatar-lg .bi {
                text-shadow: 1px 1px 0 rgba(0,0,0,0.8) !important;
            }
        }

        /* Reduced motion accessibility */
        @media (prefers-reduced-motion: reduce) {
            .bi {
                transition: none !important;
            }
        }

        /* ========== SOFT UI COMPONENTS STYLES ========== */

        /* Soft UI Chart Component */
        .soft-chart-container {
            background: var(--soft-white);
            border-radius: var(--soft-border-radius-xl);
            box-shadow: var(--soft-shadow);
            padding: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
        }

        .soft-chart-container:hover {
            transform: translateY(-5px);
            box-shadow: var(--soft-shadow-lg);
        }

        .soft-chart-header {
            background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            position: relative;
        }

        .soft-chart-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--soft-primary) 0%, var(--soft-info) 50%, var(--soft-success) 100%);
            border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
        }

        .soft-chart-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--soft-dark);
            margin: 0;
            display: flex;
            align-items: center;
        }

        .soft-chart-title .bi {
            color: var(--soft-primary) !important;
            margin-left: 0.75rem;
            font-size: 1.5rem;
        }

        .soft-chart-subtitle {
            color: var(--soft-secondary);
            font-size: 0.875rem;
            margin: 0.5rem 0 0 0;
            font-weight: 500;
        }

        .soft-chart-body {
            padding: 2rem;
            position: relative;
        }

        .soft-chart-canvas-wrapper {
            position: relative;
            height: 300px;
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.02) 0%, rgba(17, 205, 239, 0.02) 100%);
            border-radius: var(--soft-border-radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .chart-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .chart-stat-item {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.8) 100%);
            padding: 0.75rem 1rem;
            border-radius: var(--soft-border-radius);
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            flex: 1;
            min-width: 150px;
        }

        .chart-stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
        }

        .chart-stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1rem;
        }

        .chart-stat-icon.primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
        }

        .chart-stat-icon.success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            color: white;
        }

        .chart-stat-icon.info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);
            color: white;
        }

        .chart-stat-content h6 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--soft-dark);
        }

        .chart-stat-content small {
            color: var(--soft-secondary);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .chart-legend-item {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            color: var(--soft-secondary);
            font-weight: 500;
        }

        .chart-legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }

        /* User Progress Tracking Widget */
        .user-progress-widget {
            background: var(--soft-white);
            border-radius: var(--soft-border-radius-xl);
            box-shadow: var(--soft-shadow);
            padding: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .user-progress-widget:hover {
            transform: translateY(-3px);
            box-shadow: var(--soft-shadow-lg);
        }

        .user-progress-header {
            background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            position: relative;
        }

        .user-progress-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--soft-primary) 0%, var(--soft-info) 50%, var(--soft-success) 100%);
            border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
        }

        .user-progress-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--soft-dark);
            margin: 0 0 0.25rem 0;
            display: flex;
            align-items: center;
        }

        .user-progress-title .bi {
            color: var(--soft-primary);
            margin-left: 0.5rem;
            font-size: 1.25rem;
        }

        .user-progress-subtitle {
            color: var(--soft-secondary);
            font-size: 0.8rem;
            margin: 0;
            font-weight: 500;
        }

        .user-progress-body {
            padding: 1.5rem;
        }

        .progress-metric {
            margin-bottom: 1.25rem;
        }

        .progress-metric:last-child {
            margin-bottom: 0;
        }

        .progress-metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-metric-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--soft-dark);
        }

        .progress-metric-value {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .progress-metric-value.success {
            color: var(--soft-success);
        }

        .progress-metric-value.warning {
            color: var(--soft-warning);
        }

        .progress-metric-value.danger {
            color: var(--soft-danger);
        }

        .progress-bar-container {
            background: rgba(0,0,0,0.05);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-bar-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.6s ease;
            position: relative;
        }

        .progress-bar-fill.success {
            background: linear-gradient(90deg, var(--soft-success) 0%, #56ca00 100%);
        }

        .progress-bar-fill.warning {
            background: linear-gradient(90deg, var(--soft-warning) 0%, #ff8a00 100%);
        }

        .progress-bar-fill.danger {
            background: linear-gradient(90deg, var(--soft-danger) 0%, #ff1744 100%);
        }

        .progress-remaining-requests {
            font-size: 0.75rem;
            color: var(--soft-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .progress-remaining-requests .bi {
            color: var(--soft-primary);
            font-size: 0.875rem;
        }

        .progress-remaining-requests.achieved {
            color: var(--soft-success);
            font-weight: 600;
        }

        .progress-remaining-requests.achieved .bi {
            color: var(--soft-success);
        }

        .user-progress-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Progress Widget Icons */
        .progress-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            margin-left: 0.5rem;
        }

        .progress-icon.daily {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);
            color: white;
        }

        .progress-icon.weekly {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);
            color: white;
        }

        .progress-icon.monthly {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            color: white;
        }

        /* Progress Widget Animations */
        .user-progress-widget {
            animation: softFadeInUp 0.6s ease-out;
        }

        .progress-bar-fill {
            position: relative;
            overflow: hidden;
        }

        .progress-bar-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Progress Widget Status Indicators */
        .progress-metric-value {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .progress-metric-value::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }

        .progress-metric-value.success::before {
            background: var(--soft-success);
            box-shadow: 0 0 8px rgba(45, 206, 137, 0.4);
        }

        .progress-metric-value.warning::before {
            background: var(--soft-warning);
            box-shadow: 0 0 8px rgba(251, 99, 64, 0.4);
        }

        .progress-metric-value.danger::before {
            background: var(--soft-danger);
            box-shadow: 0 0 8px rgba(245, 54, 92, 0.4);
        }

        /* Enhanced hover effects for progress widgets */
        .user-progress-widget:hover .progress-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .user-progress-widget:hover .progress-bar-fill::after {
            animation-duration: 1s;
        }

        /* Progress widget header enhancements */
        .user-progress-header {
            background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
            position: relative;
            overflow: hidden;
        }

        .user-progress-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: -50px;
            width: 100px;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(94, 114, 228, 0.05), transparent);
            transform: skewX(-15deg);
            transition: right 0.6s ease;
        }

        .user-progress-widget:hover .user-progress-header::after {
            right: 100%;
        }

        /* Collapsible widget states */
        .user-progress-widget.collapsed {
            height: 80px !important;
            overflow: hidden;
        }

        .user-progress-widget.collapsed .user-progress-body {
            display: none;
        }

        .user-progress-widget.collapsed:hover {
            height: auto !important;
        }

        .user-progress-widget.collapsed:hover .user-progress-body {
            display: block;
        }

        /* Progress Summary Section */
        .progress-summary {
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.05) 0%, rgba(17, 205, 239, 0.05) 100%);
            border-radius: var(--soft-border-radius);
            padding: 1rem 1.5rem;
            border: 1px solid rgba(94, 114, 228, 0.1);
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .summary-label {
            font-size: 0.875rem;
            color: var(--soft-secondary);
            font-weight: 500;
        }

        .summary-value {
            font-size: 0.875rem;
            font-weight: 700;
        }

        .progress-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* User Dashboard Specific Styling */
        .user-dashboard .user-progress-widget {
            margin-bottom: 2rem;
        }

        .user-dashboard .progress-metric {
            background: rgba(255, 255, 255, 0.7);
            border-radius: var(--soft-border-radius);
            padding: 1rem;
            margin-bottom: 0;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .user-dashboard .progress-metric:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }

        /* Soft UI Active Users Component */
        .soft-active-users-card {
            background: var(--soft-white);
            border-radius: var(--soft-border-radius-xl);
            box-shadow: var(--soft-shadow);
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
            position: relative;
        }

        .soft-active-users-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--soft-shadow-lg);
        }

        .soft-active-users-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--soft-success) 0%, var(--soft-info) 100%);
            border-radius: var(--soft-border-radius-xl) var(--soft-border-radius-xl) 0 0;
        }

        .soft-active-users-header {
            padding: 1.5rem 2rem 1rem 2rem;
            background: linear-gradient(135deg, rgba(45, 206, 137, 0.02) 0%, rgba(17, 205, 239, 0.02) 100%);
        }

        .soft-active-users-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .soft-active-users-title h5 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--soft-dark);
            display: flex;
            align-items: center;
        }

        .soft-active-users-title .bi {
            color: var(--soft-success) !important;
            margin-left: 0.5rem;
            font-size: 1.25rem;
        }

        .soft-active-users-badge {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            box-shadow: 0 0.125rem 0.25rem rgba(45, 206, 137, 0.3);
        }

        .soft-active-users-badge .bi {
            margin-left: 0.25rem !important;
            font-size: 0.875rem !important;
            color: white !important;
        }

        .soft-active-users-main {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .soft-active-users-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--soft-dark);
            line-height: 1;
            background: linear-gradient(135deg, var(--soft-success) 0%, var(--soft-info) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .soft-active-users-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
            border-radius: var(--soft-border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(45, 206, 137, 0.3);
            position: relative;
            overflow: hidden;
        }

        .soft-active-users-icon .bi {
            color: white !important;
        }

        .soft-active-users-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .soft-active-users-card:hover .soft-active-users-icon::before {
            opacity: 1;
            animation: shimmer 1.5s ease-in-out;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .soft-active-users-body {
            padding: 0 2rem 2rem 2rem;
        }

        .soft-active-users-subtitle {
            color: var(--soft-secondary);
            font-size: 0.875rem;
            margin: 0.5rem 0 1.5rem 0;
            font-weight: 500;
        }

        /* Soft UI Statistics Cards */
        .soft-stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
            border-radius: var(--soft-border-radius);
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .soft-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
            background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,249,250,1) 100%);
        }

        .soft-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--soft-dark);
            margin-bottom: 0.25rem;
        }

        .soft-stat-label {
            font-size: 0.75rem;
            color: var(--soft-secondary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Pulse animation for live indicators */
        .pulse-dot {
            width: 8px;
            height: 8px;
            background: var(--soft-success);
            border-radius: 50%;
            margin-left: 0.25rem;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(45, 206, 137, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(45, 206, 137, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(45, 206, 137, 0);
            }
        }

        /* Responsive Design for Soft UI Components */
        @media (max-width: 768px) {
            .soft-chart-header,
            .soft-active-users-header {
                padding: 1rem 1.5rem;
            }

            .soft-chart-body,
            .soft-active-users-body {
                padding: 1.5rem;
            }

            .soft-chart-canvas-wrapper {
                height: 250px;
                padding: 0.75rem;
            }

            .chart-stats {
                flex-direction: column;
                align-items: stretch;
            }

            .chart-stat-item {
                justify-content: center;
                min-width: auto;
            }

            .chart-legend {
                gap: 1rem;
            }

            .user-progress-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .user-progress-header {
                padding: 1rem 1.25rem;
            }

            .user-progress-body {
                padding: 1.25rem;
            }

            .progress-metric {
                margin-bottom: 1rem;
            }

            .progress-metric-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .soft-active-users-main {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .soft-active-users-number {
                font-size: 2rem;
            }

            .soft-active-users-icon {
                width: 3rem;
                height: 3rem;
                font-size: 1.5rem;
            }

            .soft-chart-title {
                font-size: 1.1rem;
            }
        }

        /* Animation for Soft UI components */
        @keyframes softFadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .soft-chart-container,
        .soft-active-users-card {
            animation: softFadeInUp 0.6s ease-out;
        }

        /* Variant styles for different colored components */
        .soft-active-users-card.primary::before {
            background: linear-gradient(90deg, var(--soft-primary) 0%, #667eea 100%);
        }

        .soft-active-users-card.primary .soft-active-users-badge {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            box-shadow: 0 0.125rem 0.25rem rgba(94, 114, 228, 0.3);
        }

        .soft-active-users-card.primary .soft-active-users-number {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .soft-active-users-card.primary .soft-active-users-icon {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            box-shadow: 0 0.25rem 0.5rem rgba(94, 114, 228, 0.3);
        }

        .soft-active-users-card.warning::before {
            background: linear-gradient(90deg, var(--soft-warning) 0%, #ff8a00 100%);
        }

        .soft-active-users-card.warning .soft-active-users-badge {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);
            box-shadow: 0 0.125rem 0.25rem rgba(251, 99, 64, 0.3);
        }

        .soft-active-users-card.warning .soft-active-users-number {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .soft-active-users-card.warning .soft-active-users-icon {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);
            box-shadow: 0 0.25rem 0.5rem rgba(251, 99, 64, 0.3);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                margin: 0.5rem;
                border-radius: var(--soft-border-radius);
            }

            .main-content {
                padding: 1rem !important;
            }

            .card-body {
                padding: 1rem;
            }

            .display-5 {
                font-size: 2rem;
            }

            .display-6 {
                font-size: 1.5rem;
            }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <div class="navbar-brand-icon me-3">
                    <i class="bi bi-archive fs-4"></i>
                </div>
                <div>
                    <div class="fw-bold">CMSVS</div>
                    <small class="opacity-75">Internal System</small>
                </div>
            </a>
            
            {% if current_user %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.role.value == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="bi bi-speedometer2"></i> لوحة الإدارة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users">
                            <i class="bi bi-people"></i> إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/requests">
                            <i class="bi bi-file-earmark-text"></i> إدارة الطلبات
                        </a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-house"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/requests">
                            <i class="bi bi-file-earmark-text"></i> طلباتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/requests/new">
                            <i class="bi bi-plus-circle"></i> طلب جديد
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person"></i> الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid p-0">
        <div class="row g-0">
            {% if current_user %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="sidebar">
                {% block sidebar %}
                <div class="list-group">
                    {% if current_user.role.value == 'admin' %}
                    <a href="/admin/dashboard" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة الإدارة
                    </a>
                    <a href="/admin/users" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> المستخدمون
                    </a>
                    <a href="/admin/requests" class="list-group-item list-group-item-action">
                        <i class="bi bi-file-earmark-text"></i> الطلبات
                    </a>
                    <a href="/admin/activities" class="list-group-item list-group-item-action">
                        <i class="bi bi-activity"></i> النشاطات
                    </a>
                    {% else %}
                    <a href="/dashboard" class="list-group-item list-group-item-action">
                        <i class="bi bi-house"></i> الرئيسية
                    </a>
                    <a href="/requests" class="list-group-item list-group-item-action">
                        <i class="bi bi-file-earmark-text"></i> طلباتي
                    </a>
                    <a href="/requests/new" class="list-group-item list-group-item-action">
                        <i class="bi bi-plus-circle"></i> طلب جديد
                    </a>
                    <a href="/profile" class="list-group-item list-group-item-action">
                        <i class="bi bi-person"></i> الملف الشخصي
                    </a>
                    {% endif %}
                </div>
                {% endblock %}
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
            {% else %}
            <div class="col-12">
                <div class="main-content">
            {% endif %}
                <!-- Alerts -->
                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}
                
                {% if success %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i>
                    {{ success }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}
                
                <!-- Page Content -->
                {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Enhanced Icon Loading and Fallback System
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Bootstrap Icons are loaded
            function checkBootstrapIcons() {
                const testElement = document.createElement('i');
                testElement.className = 'bi bi-house';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);

                const computedStyle = window.getComputedStyle(testElement);
                const fontFamily = computedStyle.getPropertyValue('font-family');

                document.body.removeChild(testElement);

                return fontFamily.includes('bootstrap-icons');
            }

            // Initialize icon system
            function initializeIcons() {
                if (checkBootstrapIcons()) {
                    console.log('✓ Bootstrap Icons loaded successfully');
                    document.body.classList.add('icons-loaded');
                } else {
                    console.warn('⚠ Bootstrap Icons failed to load, using fallbacks');
                    document.body.classList.add('icons-fallback');

                    // Add fallback CSS for missing icons
                    const fallbackCSS = `
                        .bi:not([class*="fa-"])::before {
                            content: "⚫" !important;
                            font-family: system-ui, -apple-system, sans-serif !important;
                        }
                        /* Dashboard Icons */
                        .bi-speedometer2::before { content: "📊" !important; }
                        .bi-person::before { content: "👤" !important; }
                        .bi-people::before { content: "👥" !important; }
                        .bi-file-earmark-text::before { content: "📄" !important; }
                        .bi-clock::before { content: "🕐" !important; }
                        .bi-gear::before { content: "⚙️" !important; }
                        .bi-check-circle::before { content: "✅" !important; }
                        .bi-arrow-up::before { content: "↑" !important; }
                        .bi-arrow-left::before { content: "←" !important; }
                        .bi-eye::before { content: "👁️" !important; }
                        .bi-shield-check::before { content: "🛡️" !important; }
                        .bi-shield::before { content: "🛡️" !important; }
                        .bi-person-check::before { content: "✓👤" !important; }
                        .bi-play-circle::before { content: "▶️" !important; }
                        .bi-x-circle::before { content: "❌" !important; }

                        /* Navigation Icons */
                        .bi-house::before { content: "🏠" !important; }
                        .bi-plus-circle::before { content: "➕" !important; }
                        .bi-person-circle::before { content: "👤" !important; }
                        .bi-box-arrow-right::before { content: "🚪" !important; }
                        .bi-box-arrow-in-right::before { content: "🔑" !important; }
                        .bi-archive::before { content: "📦" !important; }

                        /* Activity Icons */
                        .bi-activity::before { content: "📈" !important; }
                        .bi-upload::before { content: "⬆️" !important; }

                        /* Additional Icons */
                        .bi-chevron-left::before { content: "‹" !important; }
                        .bi-chevron-right::before { content: "›" !important; }
                        .bi-download::before { content: "⬇️" !important; }
                        .bi-trash::before { content: "🗑️" !important; }
                        .bi-pencil::before { content: "✏️" !important; }
                    `;

                    const style = document.createElement('style');
                    style.textContent = fallbackCSS;
                    document.head.appendChild(style);
                }
            }

            // Force icon refresh for dynamic content
            function refreshIcons() {
                const icons = document.querySelectorAll('.bi');
                icons.forEach(icon => {
                    // Ensure font family
                    icon.style.fontFamily = '"bootstrap-icons", "Font Awesome 6 Free", system-ui, sans-serif';

                    // Fix color inheritance issues
                    const parent = icon.closest('.navbar, .nav-link, .card-stats, .avatar-lg, .avatar-sm, .btn, .list-group-item, .dropdown-item');

                    if (parent) {
                        if (parent.classList.contains('navbar') || parent.classList.contains('nav-link')) {
                            icon.style.color = 'white';
                        } else if (parent.classList.contains('card-stats') || parent.classList.contains('avatar-lg') || parent.classList.contains('avatar-sm')) {
                            icon.style.color = 'white';
                        } else if (parent.classList.contains('btn')) {
                            // Let button handle its own color
                            icon.style.color = 'inherit';
                        } else if (parent.classList.contains('list-group-item')) {
                            if (parent.classList.contains('active') || parent.matches(':hover')) {
                                icon.style.color = 'white';
                            } else {
                                icon.style.color = '#8392ab';
                            }
                        } else if (parent.classList.contains('dropdown-item')) {
                            if (parent.matches(':hover')) {
                                icon.style.color = 'white';
                            } else {
                                icon.style.color = '#8392ab';
                            }
                        }
                    }

                    // Ensure visibility
                    icon.style.opacity = '1';
                    icon.style.visibility = 'visible';
                });
            }

            // Additional function to fix color contrast issues
            function fixIconColors() {
                // Fix navbar icons
                document.querySelectorAll('.navbar .bi, .nav-link .bi, .navbar-brand .bi').forEach(icon => {
                    icon.style.color = 'white';
                });

                // Fix sidebar icons
                document.querySelectorAll('.sidebar .bi, .list-group-item .bi').forEach(icon => {
                    const parent = icon.closest('.list-group-item');
                    if (parent && (parent.classList.contains('active') || parent.matches(':hover'))) {
                        icon.style.color = 'white';
                    } else {
                        icon.style.color = '#8392ab';
                    }
                });

                // Fix card statistics icons
                document.querySelectorAll('.card-stats .bi, .avatar-lg .bi, .avatar-sm .bi').forEach(icon => {
                    icon.style.color = 'white';
                });

                // Fix button icons
                document.querySelectorAll('.btn .bi').forEach(icon => {
                    const btn = icon.closest('.btn');
                    if (btn.classList.contains('btn-outline-primary') && !btn.matches(':hover')) {
                        icon.style.color = '#5e72e4';
                    } else if (btn.classList.contains('btn-outline-primary') && btn.matches(':hover')) {
                        icon.style.color = 'white';
                    } else {
                        icon.style.color = 'inherit';
                    }
                });
            }

            // Initialize immediately
            initializeIcons();

            // Fix icon colors after initialization
            setTimeout(() => {
                refreshIcons();
                fixIconColors();
            }, 100);

            // Refresh icons after HTMX requests
            if (typeof htmx !== 'undefined') {
                htmx.on('htmx:afterSwap', () => {
                    refreshIcons();
                    fixIconColors();
                });
                htmx.on('htmx:afterSettle', () => {
                    refreshIcons();
                    fixIconColors();
                });
            }

            // Periodic check for dynamic content
            setInterval(() => {
                refreshIcons();
                fixIconColors();
            }, 3000);

            // Fix colors on hover events
            document.addEventListener('mouseover', (e) => {
                if (e.target.closest('.list-group-item, .dropdown-item, .btn-outline-primary')) {
                    fixIconColors();
                }
            });

            document.addEventListener('mouseout', (e) => {
                if (e.target.closest('.list-group-item, .dropdown-item, .btn-outline-primary')) {
                    setTimeout(fixIconColors, 50);
                }
            });
        });
        
        // File upload preview
        function previewFiles(input) {
            var preview = document.getElementById('file-preview');
            preview.innerHTML = '';
            
            if (input.files) {
                Array.from(input.files).forEach(function(file, index) {
                    var div = document.createElement('div');
                    div.className = 'alert alert-info d-flex justify-content-between align-items-center';
                    div.innerHTML = `
                        <span><i class="bi bi-file-earmark"></i> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                            <i class="bi bi-x"></i>
                        </button>
                    `;
                    preview.appendChild(div);
                });
            }
        }
        
        function removeFile(index) {
            // This is a simplified version - in a real implementation,
            // you'd need to manage the FileList properly
            var input = document.getElementById('files');
            var dt = new DataTransfer();
            var files = input.files;
            
            for (let i = 0; i < files.length; i++) {
                if (i !== index) {
                    dt.items.add(files[i]);
                }
            }
            
            input.files = dt.files;
            previewFiles(input);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
