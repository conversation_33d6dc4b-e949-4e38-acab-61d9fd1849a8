{% extends "base.html" %}

{% block title %}لوحة الإدارة - CMSVS{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="d-flex justify-content-between align-items-center mb-5">
    <div>
        <h1 class="display-6 fw-bold text-dark mb-2">
            <i class="bi bi-speedometer2 text-primary me-3"></i>
            لوحة الإدارة
        </h1>
        <p class="text-muted mb-0">نظرة عامة على النظام والإحصائيات</p>
    </div>
    <div class="card border-0 shadow-sm">
        <div class="card-body py-3 px-4">
            <div class="d-flex align-items-center">
                <div class="avatar-sm bg-primary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-3">
                    <i class="bi bi-person text-white"></i>
                </div>
                <div>
                    <h6 class="mb-0 fw-bold">مرحباً، {{ current_user.full_name }}</h6>
                    <small class="text-muted">مدير النظام</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-5 g-4">
    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-primary text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">إجمالي الطلبات</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.total }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-arrow-up me-1"></i>
                            جميع الطلبات المسجلة
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-file-earmark-text fs-3 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-warning text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">قيد المراجعة</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.pending }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-clock me-1"></i>
                            في انتظار المراجعة
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-clock fs-3 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-info text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">قيد التنفيذ</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.in_progress }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-gear me-1"></i>
                            جاري العمل عليها
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-gear fs-3 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-success text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">مكتملة</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.completed }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-check-circle me-1"></i>
                            تم إنجازها بنجاح
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-check-circle fs-3 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Soft UI Chart and Active Users Components -->
<div class="row mb-5 g-4">
    <!-- Chart Component -->
    <div class="col-lg-8">
        <div class="soft-chart-container">
            <div class="soft-chart-header">
                <h4 class="soft-chart-title">
                    <i class="bi bi-person-check"></i>
                    الطلبات المكتملة للمستخدمين شهرياً
                </h4>
                <p class="soft-chart-subtitle">
                    <i class="bi bi-people text-info me-1"></i>
                    {% if user_chart_data.datasets %}
                    عرض {{ user_chart_data.datasets|length }} مستخدم مع طلبات مكتملة
                    {% else %}
                    لا توجد بيانات للمستخدمين
                    {% endif %}
                </p>
            </div>

            <div class="soft-chart-body">
                {% if user_chart_data.datasets %}
                <div class="soft-chart-canvas-wrapper">
                    <canvas id="requestsChart"></canvas>
                </div>

                <!-- Chart Statistics Section -->
                <div class="chart-stats">
                    <div class="chart-stat-item">
                        <div class="chart-stat-content">
                            <h6>{{ user_chart_data.datasets|length }}</h6>
                            <small>المستخدمون النشطون</small>
                        </div>
                        <div class="chart-stat-icon primary">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>

                    <div class="chart-stat-item">
                        <div class="chart-stat-content">
                            <h6>{{ request_stats.completed }}</h6>
                            <small>إجمالي الطلبات المكتملة</small>
                        </div>
                        <div class="chart-stat-icon success">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>

                    <div class="chart-stat-item">
                        <div class="chart-stat-content">
                            <h6>12</h6>
                            <small>الأشهر المعروضة</small>
                        </div>
                        <div class="chart-stat-icon info">
                            <i class="bi bi-calendar-month"></i>
                        </div>
                    </div>
                </div>

                <!-- Chart Legend Section -->
                <div class="chart-legend" id="chartLegend">
                    <!-- Legend items will be populated by JavaScript -->
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="avatar-lg bg-light rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                        <i class="bi bi-graph-up fs-3 text-muted"></i>
                    </div>
                    <h5 class="text-muted fw-semibold">لا توجد بيانات للعرض</h5>
                    <p class="text-muted mb-0">لا يوجد مستخدمون بطلبات مكتملة لعرضها في الرسم البياني</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Active Users Component -->
    <div class="col-lg-4">
        <div class="soft-active-users-card">
            <div class="soft-active-users-header">
                <div class="soft-active-users-title">
                    <h5>
                        <i class="bi bi-people-fill"></i>
                        المستخدمون النشطون
                    </h5>
                    <div class="soft-active-users-badge">
                        <i class="bi bi-circle-fill pulse-dot"></i>
                        مباشر
                    </div>
                </div>

                <div class="soft-active-users-main">
                    <div class="soft-active-users-number">{{ user_stats.active }}</div>
                    <div class="soft-active-users-icon">
                        <i class="bi bi-person-check-fill"></i>
                    </div>
                </div>

                <p class="soft-active-users-subtitle">
                    <i class="bi bi-arrow-up text-success me-1"></i>
                    {% if user_stats.total > 0 %}
                    {{ ((user_stats.active / user_stats.total * 100)|round(1)) }}% من إجمالي المستخدمين
                    {% else %}
                    لا توجد بيانات
                    {% endif %}
                </p>
            </div>

            <div class="soft-active-users-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="soft-stat-card">
                            <div class="soft-stat-number">{{ user_stats.total }}</div>
                            <div class="soft-stat-label">الإجمالي</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="soft-stat-card">
                            <div class="soft-stat-number">{{ user_stats.admins }}</div>
                            <div class="soft-stat-label">المديرون</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="soft-stat-card">
                            <div class="soft-stat-number">{{ user_stats.active }}</div>
                            <div class="soft-stat-label">النشطون</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Soft UI Statistics Cards -->
<div class="row mb-5 g-4">
    <div class="col-md-4">
        <div class="soft-active-users-card primary">
            <div class="soft-active-users-header">
                <div class="soft-active-users-title">
                    <h5>
                        <i class="bi bi-file-earmark-text"></i>
                        الطلبات الجديدة
                    </h5>
                    <div class="soft-active-users-badge">
                        <i class="bi bi-circle-fill pulse-dot"></i>
                        جديد
                    </div>
                </div>

                <div class="soft-active-users-main">
                    <div class="soft-active-users-number">{{ request_stats.pending }}</div>
                    <div class="soft-active-users-icon">
                        <i class="bi bi-plus-circle-fill"></i>
                    </div>
                </div>

                <p class="soft-active-users-subtitle">
                    <i class="bi bi-arrow-up text-primary me-1"></i>
                    تحتاج للمراجعة
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="soft-active-users-card">
            <div class="soft-active-users-header">
                <div class="soft-active-users-title">
                    <h5>
                        <i class="bi bi-check-circle"></i>
                        المهام المكتملة
                    </h5>
                    <div class="soft-active-users-badge">
                        <i class="bi bi-circle-fill pulse-dot"></i>
                        محدث
                    </div>
                </div>

                <div class="soft-active-users-main">
                    <div class="soft-active-users-number">{{ request_stats.completed }}</div>
                    <div class="soft-active-users-icon">
                        <i class="bi bi-check-all"></i>
                    </div>
                </div>

                <p class="soft-active-users-subtitle">
                    <i class="bi bi-arrow-up text-success me-1"></i>
                    تم إنجازها بنجاح
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="soft-active-users-card warning">
            <div class="soft-active-users-header">
                <div class="soft-active-users-title">
                    <h5>
                        <i class="bi bi-exclamation-triangle"></i>
                        قيد التنفيذ
                    </h5>
                    <div class="soft-active-users-badge">
                        <i class="bi bi-circle-fill pulse-dot"></i>
                        نشط
                    </div>
                </div>

                <div class="soft-active-users-main">
                    <div class="soft-active-users-number">{{ request_stats.in_progress }}</div>
                    <div class="soft-active-users-icon">
                        <i class="bi bi-clock-fill"></i>
                    </div>
                </div>

                <p class="soft-active-users-subtitle">
                    <i class="bi bi-gear text-warning me-1"></i>
                    جاري العمل عليها
                </p>
            </div>
        </div>
    </div>
</div>

<!-- User Progress Tracking Widgets -->
{% if user_progress_data %}
<div class="row mb-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 py-3"
                 style="cursor: pointer;"
                 onclick="toggleProgressSection()"
                 id="progressSectionHeader">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1 fw-bold text-dark">
                            <i class="bi bi-speedometer text-primary me-2"></i>
                            تتبع تقدم المستخدمين
                            <span class="badge bg-primary ms-2">{{ user_progress_data|length }}</span>
                        </h4>
                        <p class="text-muted mb-0 small">مراقبة الأهداف اليومية والأسبوعية والشهرية للمستخدمين</p>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); toggleAllWidgets()">
                                <i class="bi bi-arrows-expand me-1"></i>
                                توسيع الكل
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="event.stopPropagation(); refreshProgressData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                تحديث
                            </button>
                        </div>
                        <i class="bi bi-chevron-down text-muted" id="progressSectionIcon"></i>
                    </div>
                </div>
            </div>

            <div class="card-body p-3" id="progressSectionBody">

        <div class="user-progress-grid">
            {% for user_progress in user_progress_data %}
            <div class="user-progress-widget" data-user-id="{{ user_progress.user_id }}">
                <div class="user-progress-header">
                    <h5 class="user-progress-title">
                        <i class="bi bi-person-circle"></i>
                        {{ user_progress.full_name }}
                    </h5>
                    <p class="user-progress-subtitle">@{{ user_progress.username }}</p>
                </div>

                <div class="user-progress-body">
                    <!-- Daily Progress -->
                    <div class="progress-metric">
                        <div class="progress-metric-header">
                            <div class="d-flex align-items-center">
                                <div class="progress-icon daily">
                                    <i class="bi bi-sun"></i>
                                </div>
                                <span class="progress-metric-label">الهدف اليومي</span>
                            </div>
                            <span class="progress-metric-value {{ user_progress.daily.status }}">
                                {{ user_progress.daily.completed }}/{{ user_progress.daily.goal }} - {{ user_progress.daily.percentage }}%
                            </span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill {{ user_progress.daily.status }}"
                                 style="width: {{ user_progress.daily.percentage }}%"></div>
                        </div>
                        <div class="progress-remaining-requests {% if user_progress.daily.remaining == 0 %}achieved{% endif %}">
                            {% if user_progress.daily.remaining == 0 %}
                            <i class="bi bi-check-circle-fill me-1"></i>
                            {% else %}
                            <i class="bi bi-target me-1"></i>
                            {% endif %}
                            {{ user_progress.daily.remaining_text }}
                        </div>
                    </div>

                    <!-- Weekly Progress -->
                    <div class="progress-metric">
                        <div class="progress-metric-header">
                            <div class="d-flex align-items-center">
                                <div class="progress-icon weekly">
                                    <i class="bi bi-calendar-week"></i>
                                </div>
                                <span class="progress-metric-label">الهدف الأسبوعي</span>
                            </div>
                            <span class="progress-metric-value {{ user_progress.weekly.status }}">
                                {{ user_progress.weekly.completed }}/{{ user_progress.weekly.goal }} - {{ user_progress.weekly.percentage }}%
                            </span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill {{ user_progress.weekly.status }}"
                                 style="width: {{ user_progress.weekly.percentage }}%"></div>
                        </div>
                        <div class="progress-remaining-requests {% if user_progress.weekly.remaining == 0 %}achieved{% endif %}">
                            {% if user_progress.weekly.remaining == 0 %}
                            <i class="bi bi-check-circle-fill me-1"></i>
                            {% else %}
                            <i class="bi bi-bullseye me-1"></i>
                            {% endif %}
                            {{ user_progress.weekly.remaining_text }}
                        </div>
                    </div>

                    <!-- Monthly Progress -->
                    <div class="progress-metric">
                        <div class="progress-metric-header">
                            <div class="d-flex align-items-center">
                                <div class="progress-icon monthly">
                                    <i class="bi bi-calendar-month"></i>
                                </div>
                                <span class="progress-metric-label">الهدف الشهري</span>
                            </div>
                            <span class="progress-metric-value {{ user_progress.monthly.status }}">
                                {{ user_progress.monthly.completed }}/{{ user_progress.monthly.goal }} - {{ user_progress.monthly.percentage }}%
                            </span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill {{ user_progress.monthly.status }}"
                                 style="width: {{ user_progress.monthly.percentage }}%"></div>
                        </div>
                        <div class="progress-remaining-requests {% if user_progress.monthly.remaining == 0 %}achieved{% endif %}">
                            {% if user_progress.monthly.remaining == 0 %}
                            <i class="bi bi-check-circle-fill me-1"></i>
                            {% else %}
                            <i class="bi bi-trophy me-1"></i>
                            {% endif %}
                            {{ user_progress.monthly.remaining_text }}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            </div>
        </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Requests -->
<div class="card border-0">
    <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center py-4">
        <div>
            <h4 class="mb-1 fw-bold text-dark">
                <i class="bi bi-file-earmark-text text-primary me-2"></i>
                الطلبات الأخيرة
            </h4>
            <p class="text-muted mb-0 small">آخر الطلبات المقدمة في النظام</p>
        </div>
        <a href="/admin/requests" class="btn btn-primary btn-sm px-4 py-2 fw-semibold">
            <i class="bi bi-arrow-left me-2"></i>
            عرض الكل
        </a>
    </div>
    <div class="card-body p-0">
        {% if recent_requests %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="border-0 fw-semibold text-dark ps-4">رقم الطلب</th>
                        <th class="border-0 fw-semibold text-dark">العنوان</th>
                        <th class="border-0 fw-semibold text-dark">مقدم الطلب</th>
                        <th class="border-0 fw-semibold text-dark">الحالة</th>
                        <th class="border-0 fw-semibold text-dark">التاريخ</th>
                        <th class="border-0 fw-semibold text-dark pe-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in recent_requests %}
                    <tr class="border-0">
                        <td class="ps-4 py-3">
                            <span class="badge bg-light text-dark fw-semibold px-3 py-2">{{ req.request_number }}</span>
                        </td>
                        <td class="py-3">
                            <div class="fw-semibold text-dark">{{ req.request_title }}</div>
                        </td>
                        <td class="py-3">
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-secondary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="bi bi-person text-white small"></i>
                                </div>
                                <span class="fw-semibold">{{ req.user.full_name }}</span>
                            </div>
                        </td>
                        <td class="py-3">
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning status-badge fw-semibold px-3 py-2">قيد المراجعة</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info status-badge fw-semibold px-3 py-2">قيد التنفيذ</span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success status-badge fw-semibold px-3 py-2">مكتمل</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger status-badge fw-semibold px-3 py-2">مرفوض</span>
                            {% endif %}
                        </td>
                        <td class="py-3">
                            <span class="text-muted fw-semibold">{{ req.created_at.strftime('%Y-%m-%d') }}</span>
                        </td>
                        <td class="pe-4 py-3">
                            <div class="d-flex gap-2">
                                <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary px-3">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض
                                </a>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle px-3"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-gear me-1"></i>
                                        إجراءات
                                    </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="in_progress">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle text-info"></i> قيد التنفيذ
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-check-circle text-success"></i> مكتمل
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-x-circle text-danger"></i> مرفوض
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="avatar-lg bg-light rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                <i class="bi bi-file-earmark-text fs-3 text-muted"></i>
            </div>
            <h5 class="text-muted fw-semibold">لا توجد طلبات</h5>
            <p class="text-muted mb-0">لم يتم تقديم أي طلبات حتى الآن</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Initialize Soft UI Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('requestsChart');
    if (ctx) {
        const chartCtx = ctx.getContext('2d');

        // User chart data from backend
        const userChartData = {{ user_chart_data|tojson|safe }};

        // Soft UI Color Palette (matching the reference)
        const softColors = [
            '#5e72e4', // Primary
            '#11cdef', // Info
            '#2dce89', // Success
            '#fb6340', // Warning
            '#f5365c', // Danger
            '#ffd600', // Yellow
            '#36b9cc', // Cyan
            '#6f42c1', // Purple
            '#e83e8c', // Pink
            '#fd7e14'  // Orange
        ];

        // Create gradients for each dataset
        const createGradient = (color, alpha = 0.1) => {
            const gradient = chartCtx.createLinearGradient(0, 0, 0, 300);
            gradient.addColorStop(0, color + Math.round(alpha * 255 * 3).toString(16).padStart(2, '0'));
            gradient.addColorStop(0.5, color + Math.round(alpha * 255 * 2).toString(16).padStart(2, '0'));
            gradient.addColorStop(1, color + Math.round(alpha * 255).toString(16).padStart(2, '0'));
            return gradient;
        };

        // Enhance datasets with Soft UI styling
        const enhancedDatasets = (userChartData.datasets || []).map((dataset, index) => {
            const color = softColors[index % softColors.length];
            return {
                ...dataset,
                borderColor: color,
                backgroundColor: createGradient(color, 0.1),
                pointBackgroundColor: color,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointHoverBackgroundColor: color,
                pointHoverBorderColor: '#ffffff',
                pointHoverBorderWidth: 3,
                borderWidth: 3,
                fill: true,
                tension: 0.4
            };
        });

        // Prepare chart data with enhanced styling
        const chartData = {
            labels: userChartData.labels || ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: enhancedDatasets
        };

        // Only create chart if there are datasets
        if (chartData.datasets && chartData.datasets.length > 0) {
            new Chart(chartCtx, {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false // We'll use custom legend below
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#32325d',
                        bodyColor: '#32325d',
                        borderColor: '#e9ecef',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        rtl: true,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const datasetLabel = context.dataset.label || '';
                                const value = context.parsed.y;
                                const username = context.dataset.username || '';
                                return `${datasetLabel} (${username}): ${value} طلب مكتمل`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#8392ab',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: 10
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#8392ab',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: 10
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#5e72e4',
                        hoverBorderColor: '#ffffff',
                        hoverBorderWidth: 3
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        // Generate custom legend matching Soft UI style
        generateCustomLegend(enhancedDatasets);
        }
    }

    // Function to generate custom legend
    function generateCustomLegend(datasets) {
        const legendContainer = document.getElementById('chartLegend');
        if (!legendContainer || !datasets || datasets.length === 0) return;

        legendContainer.innerHTML = '';

        datasets.forEach((dataset, index) => {
            const legendItem = document.createElement('div');
            legendItem.className = 'chart-legend-item';

            const colorSpan = document.createElement('span');
            colorSpan.className = 'chart-legend-color';
            colorSpan.style.background = dataset.borderColor;

            const textSpan = document.createElement('span');
            textSpan.textContent = dataset.label;

            legendItem.appendChild(colorSpan);
            legendItem.appendChild(textSpan);
            legendContainer.appendChild(legendItem);
        });
    }

    // Add smooth animations to statistics numbers
    function animateNumbers() {
        const numbers = document.querySelectorAll('.soft-active-users-number');
        numbers.forEach(number => {
            const finalValue = parseInt(number.textContent);
            if (isNaN(finalValue)) return;

            let currentValue = 0;
            const increment = finalValue / 50;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                number.textContent = Math.floor(currentValue);
            }, 30);
        });
    }

    // Start animations after a short delay
    setTimeout(animateNumbers, 500);

    // Initialize progress widgets
    initializeProgressWidgets();
});

// Progress Widget Functions
function initializeProgressWidgets() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-bar-fill');
    progressBars.forEach((bar, index) => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';

        setTimeout(() => {
            bar.style.width = targetWidth;
        }, 200 + (index * 100));
    });

    // Add hover effects to widgets
    const widgets = document.querySelectorAll('.user-progress-widget');
    widgets.forEach(widget => {
        widget.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });

        widget.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

function toggleAllWidgets() {
    const widgets = document.querySelectorAll('.user-progress-widget');
    const button = event.target.closest('button');
    const isExpanded = button.textContent.includes('توسيع');

    widgets.forEach(widget => {
        const body = widget.querySelector('.user-progress-body');
        if (isExpanded) {
            body.style.display = 'block';
            widget.style.height = 'auto';
        } else {
            body.style.display = 'none';
            widget.style.height = '80px';
        }
    });

    // Update button text
    if (isExpanded) {
        button.innerHTML = '<i class="bi bi-arrows-collapse me-1"></i>طي الكل';
    } else {
        button.innerHTML = '<i class="bi bi-arrows-expand me-1"></i>توسيع الكل';
    }
}

function refreshProgressData() {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="bi bi-arrow-clockwise me-1 spin"></i>جاري التحديث...';
    button.disabled = true;

    // Simulate refresh (in real implementation, this would make an AJAX call)
    setTimeout(() => {
        // Animate progress bars again
        const progressBars = document.querySelectorAll('.progress-bar-fill');
        progressBars.forEach(bar => {
            const currentWidth = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = currentWidth;
            }, 100);
        });

        // Reset button
        button.innerHTML = originalContent;
        button.disabled = false;

        // Show success message
        showNotification('تم تحديث بيانات التقدم بنجاح', 'success');
    }, 2000);
}

function toggleProgressSection() {
    const body = document.getElementById('progressSectionBody');
    const icon = document.getElementById('progressSectionIcon');
    const header = document.getElementById('progressSectionHeader');

    if (body.style.display === 'none') {
        body.style.display = 'block';
        icon.className = 'bi bi-chevron-down text-muted';
        header.classList.remove('collapsed');
    } else {
        body.style.display = 'none';
        icon.className = 'bi bi-chevron-up text-muted';
        header.classList.add('collapsed');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Add CSS for spinning animation
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
